#!/bin/bash

# DigWis Panel 跨平台兼容性测试脚本
# 测试系统监控功能在 macOS 和 Linux 上的兼容性

echo "🧪 DigWis Panel 跨平台兼容性测试"
echo "=================================="

# 检测操作系统
OS_TYPE=$(uname -s)
echo "📋 操作系统: $OS_TYPE"

# 测试系统信息获取命令
echo ""
echo "🔍 测试系统信息获取命令:"
echo "--------------------------------"

if [[ "$OS_TYPE" == "Darwin" ]]; then
    echo "✅ macOS 系统检测到"
    echo ""
    
    echo "🖥️  CPU 信息:"
    sysctl -n machdep.cpu.brand_string 2>/dev/null || echo "❌ CPU信息获取失败"
    
    echo ""
    echo "💾 内存信息:"
    sysctl -n hw.memsize 2>/dev/null | awk '{printf "总内存: %.2f GB\n", $1/1024/1024/1024}' || echo "❌ 内存信息获取失败"
    
    echo ""
    echo "📊 系统负载:"
    sysctl -n vm.loadavg 2>/dev/null || echo "❌ 负载信息获取失败"
    
    echo ""
    echo "⏱️  系统运行时间:"
    sysctl -n kern.boottime 2>/dev/null || echo "❌ 运行时间获取失败"
    
    echo ""
    echo "🌐 网络统计:"
    netstat -ibn | head -5 2>/dev/null || echo "❌ 网络统计获取失败"
    
    echo ""
    echo "🔧 系统版本:"
    sw_vers 2>/dev/null || echo "❌ 系统版本获取失败"
    
    echo ""
    echo "🔩 内核版本:"
    uname -r 2>/dev/null || echo "❌ 内核版本获取失败"
    
    echo ""
    echo "🚀 服务列表 (前5个):"
    launchctl list | head -6 2>/dev/null || echo "❌ 服务列表获取失败"
    
elif [[ "$OS_TYPE" == "Linux" ]]; then
    echo "✅ Linux 系统检测到"
    echo ""
    
    echo "🖥️  CPU 信息:"
    grep "model name" /proc/cpuinfo | head -1 2>/dev/null || echo "❌ CPU信息获取失败"
    
    echo ""
    echo "💾 内存信息:"
    grep -E "MemTotal|MemAvailable" /proc/meminfo 2>/dev/null || echo "❌ 内存信息获取失败"
    
    echo ""
    echo "📊 系统负载:"
    cat /proc/loadavg 2>/dev/null || echo "❌ 负载信息获取失败"
    
    echo ""
    echo "⏱️  系统运行时间:"
    cat /proc/uptime 2>/dev/null || echo "❌ 运行时间获取失败"
    
    echo ""
    echo "🌐 网络统计:"
    head -5 /proc/net/dev 2>/dev/null || echo "❌ 网络统计获取失败"
    
    echo ""
    echo "🔧 系统版本:"
    cat /etc/os-release 2>/dev/null | head -3 || echo "❌ 系统版本获取失败"
    
    echo ""
    echo "🔩 内核版本:"
    cat /proc/version 2>/dev/null || echo "❌ 内核版本获取失败"
    
    echo ""
    echo "🚀 服务列表 (前5个):"
    systemctl list-units --type=service --no-pager | head -8 2>/dev/null || echo "❌ 服务列表获取失败"
    
else
    echo "❌ 未知操作系统: $OS_TYPE"
fi

echo ""
echo "🌐 测试 DigWis Panel API 端点:"
echo "--------------------------------"

# 检查服务是否运行
if curl -s http://localhost:9090 > /dev/null 2>&1; then
    echo "✅ DigWis Panel 服务正在运行"
    
    echo ""
    echo "📊 测试系统统计 API:"
    curl -s "http://localhost:9090/api/dashboard/stats" | head -c 200
    echo "..."
    
    echo ""
    echo ""
    echo "🖥️  测试系统详情 API:"
    curl -s "http://localhost:9090/api/dashboard/system-details" | head -c 200
    echo "..."
    
    echo ""
    echo ""
    echo "💾 测试内存信息 API:"
    curl -s "http://localhost:9090/api/dashboard/memory-info" | head -c 200
    echo "..."
    
else
    echo "❌ DigWis Panel 服务未运行"
    echo "   请先启动服务: make dev 或 make run-local"
fi

echo ""
echo ""
echo "📋 跨平台兼容性总结:"
echo "=================================="
echo "✅ 认证系统: 支持 macOS (dscl) 和 Linux (su)"
echo "✅ 系统监控: 自动检测操作系统并使用相应命令"
echo "✅ 用户权限: 适配不同系统的用户ID范围"
echo "✅ 开发环境: Air 热重载支持两个平台"
echo ""
echo "🚀 部署建议:"
echo "  • 开发: 使用 make dev (Air 热重载)"
echo "  • 生产: 使用 make release-build 构建"
echo "  • 测试: 在目标平台上测试所有功能"
echo ""
echo "⚠️  注意事项:"
echo "  • Linux 部署时会自动使用 Linux 特定的命令"
echo "  • macOS 开发时会自动使用 macOS 特定的命令"
echo "  • 代码会根据 runtime.GOOS 自动选择实现"
